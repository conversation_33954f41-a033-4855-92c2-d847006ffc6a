#!/usr/bin/env python3
"""
Script para testar conexão com banco de dados PostgreSQL via URL
Autor: Assistente AI
Data: 2024
"""

import psycopg2
import sys
from urllib.parse import urlparse
import os
from typing import Optional


def test_postgres_connection(database_url: str) -> bool:
    """
    Testa a conexão com o banco de dados PostgreSQL usando uma URL de conexão.

    Args:
        database_url (str): URL de conexão no formato:
                           postgresql://username:password@host:port/database
                           ou postgres://username:password@host:port/database

    Returns:
        bool: True se a conexão foi bem-sucedida, False caso contrário
    """
    try:
        print(f"Tentando conectar ao banco de dados...")
        print(f"URL: {database_url.replace(database_url.split('@')[0].split('//')[1], '***:***')}")

        # Conecta ao banco de dados
        connection = psycopg2.connect(database_url)

        # Cria um cursor para executar comandos
        cursor = connection.cursor()

        # Testa a conexão executando uma query simples
        cursor.execute("SELECT version();")
        db_version = cursor.fetchone()

        print("✅ Conexão bem-sucedida!")
        print(f"Versão do PostgreSQL: {db_version[0]}")

        # Testa algumas informações básicas do banco
        cursor.execute("SELECT current_database();")
        current_db = cursor.fetchone()[0]
        print(f"Banco de dados atual: {current_db}")

        cursor.execute("SELECT current_user;")
        current_user = cursor.fetchone()[0]
        print(f"Usuário atual: {current_user}")

        # Fecha cursor e conexão
        cursor.close()
        connection.close()

        return True

    except psycopg2.Error as e:
        print(f"❌ Erro de PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False


def validate_url(url: str) -> bool:
    """
    Valida se a URL está no formato correto para PostgreSQL.

    Args:
        url (str): URL a ser validada

    Returns:
        bool: True se a URL é válida, False caso contrário
    """
    try:
        parsed = urlparse(url)

        # Verifica se o esquema é postgresql ou postgres
        if parsed.scheme not in ['postgresql', 'postgres']:
            print(f"❌ Esquema inválido: {parsed.scheme}. Use 'postgresql' ou 'postgres'")
            return False

        # Verifica se tem host
        if not parsed.hostname:
            print("❌ Host não especificado na URL")
            return False

        # Verifica se tem banco de dados
        if not parsed.path or parsed.path == '/':
            print("❌ Nome do banco de dados não especificado na URL")
            return False

        return True

    except Exception as e:
        print(f"❌ Erro ao validar URL: {e}")
        return False


def get_connection_info(url: str) -> dict:
    """
    Extrai informações da URL de conexão.

    Args:
        url (str): URL de conexão

    Returns:
        dict: Dicionário com informações da conexão
    """
    parsed = urlparse(url)

    return {
        'scheme': parsed.scheme,
        'username': parsed.username,
        'password': '***' if parsed.password else None,
        'hostname': parsed.hostname,
        'port': parsed.port or 5432,
        'database': parsed.path.lstrip('/')
    }


def main():
    """Função principal do script."""
    print("=" * 60)
    print("TESTE DE CONEXÃO COM POSTGRESQL")
    print("=" * 60)

    # URL padrão para testes (modifique conforme necessário)
    DEFAULT_DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/postgres"

    # Verifica se a URL foi passada como argumento
    if len(sys.argv) > 1:
        if sys.argv[1] == "--default" or sys.argv[1] == "-d":
            database_url = DEFAULT_DATABASE_URL
            print("🔧 Usando URL padrão para teste")
        else:
            database_url = sys.argv[1]
    else:
        # Tenta pegar da variável de ambiente
        database_url = os.getenv('DATABASE_URL')

        if not database_url:
            print("Como usar:")
            print("1. python test_postgres_connection.py 'postgresql://user:pass@host:port/db'")
            print("2. python test_postgres_connection.py --default  (usa URL padrão)")
            print("3. python test_postgres_connection.py -d  (usa URL padrão)")
            print("4. Definir variável de ambiente DATABASE_URL")
            print(f"\nURL padrão configurada: {DEFAULT_DATABASE_URL}")
            print("\nExemplos de URL:")
            print("postgresql://postgres:senha123@localhost:5432/meudb")
            print("postgres://user:<EMAIL>:5432/producao")

            # Oferece opções ao usuário
            print("\nOpções:")
            print("1. Digite uma URL personalizada")
            print("2. Use a URL padrão (pressione Enter)")
            print("3. Sair (digite 'q')")

            user_input = input("\nEscolha uma opção ou digite a URL: ").strip()

            if user_input.lower() == 'q':
                print("👋 Saindo...")
                sys.exit(0)
            elif user_input == '' or user_input == '2':
                database_url = DEFAULT_DATABASE_URL
                print("🔧 Usando URL padrão")
            elif user_input == '1':
                database_url = input("Digite a URL de conexão: ").strip()
                if not database_url:
                    print("❌ URL não fornecida. Saindo...")
                    sys.exit(1)
            else:
                database_url = user_input

    print("\nInformações da conexão:")
    info = get_connection_info(database_url)
    for key, value in info.items():
        print(f"  {key.capitalize()}: {value}")

    print("\n" + "-" * 40)

    # Valida a URL
    if not validate_url(database_url):
        print("❌ URL inválida. Saindo...")
        sys.exit(1)

    # Testa a conexão
    success = test_postgres_connection(database_url)

    print("\n" + "=" * 60)
    if success:
        print("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        sys.exit(0)
    else:
        print("💥 TESTE FALHOU!")
        print("\nDicas para solução de problemas:")
        print("- Verifique se o PostgreSQL está rodando")
        print("- Confirme as credenciais (usuário/senha)")
        print("- Verifique se o host e porta estão corretos")
        print("- Confirme se o banco de dados existe")
        print("- Verifique as configurações de firewall")
        sys.exit(1)


if __name__ == "__main__":
    main()
