#!/usr/bin/env python3
"""
Teste simples de conexão PostgreSQL
"""

import psycopg2

# URL de conexão - modifique conforme necessário
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/postgres"

def test_connection():
    try:
        print("Testando conexão...")

        # Conecta ao banco
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()

        # Testa com uma query simples
        cursor.execute("SELECT version();")
        result = cursor.fetchone()
        version = result[0] if result else "Versão não encontrada"

        print("✅ Conexão OK!")
        print(f"PostgreSQL: {version}")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    test_connection()
