#!/usr/bin/env python3
"""
Exemplos de configuração para teste de conexão PostgreSQL
Modifique as URLs conforme seu ambiente
"""

# URLs de exemplo para diferentes ambientes
DATABASE_URLS = {
    # Desenvolvimento local (padrão)
    'local_default': 'postgresql://postgres:postgres@localhost:5432/postgres',
    
    # Desenvolvimento local com senha personalizada
    'local_custom': 'postgresql://postgres:minhasenha@localhost:5432/meudb',
    
    # Docker local
    'docker_local': 'postgresql://postgres:postgres@localhost:5433/testdb',
    
    # Servidor remoto
    'remote_server': '*********************************************/producao',
    
    # Cloud (exemplo Heroku)
    'heroku_example': 'postgres://user:<EMAIL>:5432/dbname',
    
    # Cloud (exemplo Railway)
    'railway_example': 'postgresql://postgres:<EMAIL>:5432/railway',
    
    # Cloud (exemplo Supabase)
    'supabase_example': 'postgresql://postgres:<EMAIL>:5432/postgres',
    
    # Localhost com porta não padrão
    'localhost_custom_port': 'postgresql://postgres:postgres@localhost:5433/testdb',
}

def print_examples():
    """Imprime todos os exemplos de URL disponíveis."""
    print("=" * 70)
    print("EXEMPLOS DE URLs DE CONEXÃO POSTGRESQL")
    print("=" * 70)
    
    for name, url in DATABASE_URLS.items():
        print(f"\n{name.upper().replace('_', ' ')}:")
        print(f"  {url}")
    
    print("\n" + "=" * 70)
    print("Para usar um exemplo específico:")
    print("python test_postgres_connection.py 'URL_AQUI'")
    print("\nPara usar a URL padrão:")
    print("python test_postgres_connection.py --default")

if __name__ == "__main__":
    print_examples()
